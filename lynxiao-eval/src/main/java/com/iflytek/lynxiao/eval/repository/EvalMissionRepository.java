package com.iflytek.lynxiao.eval.repository;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMission;
import com.iflytek.lynxiao.eval.autogen.generated.repository.GeneratedEvalMissionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Transactional(rollbackFor = Exception.class)
public interface EvalMissionRepository extends GeneratedEvalMissionRepository {

    @Query("SELECT m FROM GeneratedEvalMission m WHERE m.deleted is false AND m.catalogCode in :catalogCodes AND  (LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedEvalMission> search(@Param("search") String search, @Param("catalogCodes") List<String> catalogCodes, Pageable pageable);

    @Query("SELECT m FROM GeneratedEvalMission m WHERE m.deleted is false AND m.catalogCode in :catalogCodes")
    Page<GeneratedEvalMission> findAllByDeletedFalse(@Param("catalogCodes") List<String> catalogCodes, Pageable pageable);

    @Query("SELECT DISTINCT m.label FROM GeneratedEvalMission m WHERE m.deleted is false AND m.label is not null ")
    List<String> findAllLabelList();

    @Query("SELECT m FROM GeneratedEvalMission m " +
            "WHERE m.id IN :ids " +
            "AND m.deleted = false " +
            "AND m.enabled = true " +
            "AND m.status = 2 " +
            "AND :currentTimestamp BETWEEN m.beginTime AND m.endTime")
    List<GeneratedEvalMission> findAllByIdInAndActiveAndWithinTimeRange(
            @Param("ids") List<Long> ids,
            @Param("currentTimestamp") Instant currentTimestamp);

    @Modifying
    @Query("UPDATE GeneratedEvalMission m SET m.ascribeStatus = :status WHERE m.id = :id")
    void updateAscribeStatus(@Param("id") Long id, @Param("status") int status);

    @Modifying
    @Query("UPDATE GeneratedEvalMission m SET " +
            "m.ascribeStartTs = CASE WHEN :ascribeStartTs IS NOT NULL THEN :ascribeStartTs ELSE m.ascribeStartTs END, " +
            "m.ascribeEndTs = CASE WHEN :ascribeEndTs IS NOT NULL THEN :ascribeEndTs ELSE m.ascribeEndTs END " +
            "WHERE m.id = :id")
    void updateAscribeTimestamps(@Param("id") Long id,
                                 @Param("ascribeStartTs") Instant ascribeStartTs,
                                 @Param("ascribeEndTs") Instant ascribeEndTs);

}