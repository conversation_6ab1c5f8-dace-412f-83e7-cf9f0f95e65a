package com.iflytek.lynxiao.eval.component.core;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.feign.asset.AssetPortalApi;
import com.iflytek.lynxiao.common.feign.region.DomainBlackApi;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.domain.AssetOperationType;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetKindCode;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import com.iflytek.lynxiao.data.dto.asset.IdxBucketExtConfig;
import com.iflytek.lynxiao.eval.component.domain.ExistCheckResult;
import com.iflytek.lynxiao.eval.component.domain.PreEvalDoc;
import com.iflytek.lynxiao.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.util.*;

/**
 * 分析文档Url是否存在数据桶中
 *
 * <AUTHOR>  2025/3/6 09:12
 */
@Slf4j
@Service
public class ExistService {

    private final UrlDocConverter urlDocConverter;
    private final AssetPortalApi assetPortalApi;
    private final DomainBlackApi domainBlackApi;

    public ExistService(UrlDocConverter urlDocConverter, AssetPortalApi assetPortalApi, DomainBlackApi domainBlackApi) {
        this.urlDocConverter = urlDocConverter;
        this.assetPortalApi = assetPortalApi;
        this.domainBlackApi = domainBlackApi;
    }

    public ExistCheckResult process(WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        ExistCheckResult existCheckResult = new ExistCheckResult();
        String id = goodCaseContext.getInput().getGoodId();
        String url = goodCaseContext.getInput().getGoodUrl();
        //实际执行的节点列表
        List<String> executedNodeIds = goodCaseContext.getTraceLogs().stream().map(TraceLogItem::getNodeId).toList();

        // 获取索引库桶编码
        Set<String> indexCodes = WorkflowUtil.findIndexCodes(workflowProcess, executedNodeIds);
        log.debug("indexCodes:{}", indexCodes);

        // 根据索引库桶编码获取关联的数据集桶，根据数据集桶获取源数据桶
        List<AssetBucketDTO> bucketList = this.assetPortalApi.findAllSourceBuckets(indexCodes.stream().findFirst().orElseThrow(() -> new PandoraException("No index codes found.")));

        // 进行存在性检查
        process(existCheckResult, bucketList, goodCaseContext);

        if (StringUtils.isNotBlank(url) && goodCaseContext.getDoc() == null) {
            // 平台没有存储，去dataapi获取
            Optional<PreEvalDoc> crawledDoc = urlDocConverter.fetchDoc(url);
            goodCaseContext.setDoc(JSONObject.from(crawledDoc.orElse(null)));
        }

        log.debug("id:{}, url:{} url exist check result:{}", id, url, existCheckResult);
        return existCheckResult;
    }

    private void process(ExistCheckResult existCheckResult, List<AssetBucketDTO> bucketList, GoodCaseContext goodCaseContext) {
        //处理顺序：索引库->数据集->站点、文档
        existCheck4Idx(existCheckResult, goodCaseContext, group(bucketList, AssetKindCode.IDX));

        if (existCheckResult.isEnd()) {
            return;
        }
        existCheck4Set(existCheckResult, goodCaseContext, group(bucketList, AssetKindCode.SET));

        if (existCheckResult.isEnd()) {
            return;
        }

        List<AssetBucketDTO> webAndDocBucketDTOs = new ArrayList<>();
        webAndDocBucketDTOs.addAll(group(bucketList, AssetKindCode.WEB));
        webAndDocBucketDTOs.addAll(group(bucketList, AssetKindCode.DOC));

        existCheck4Raw(existCheckResult, goodCaseContext, webAndDocBucketDTOs);
    }

    private void existCheck4Idx(ExistCheckResult existCheckResult, GoodCaseContext goodCaseContext, List<AssetBucketDTO> bucketDTOList4Idx) {

        List<AssetCell> foundCells = new ArrayList<>();
        List<AssetCell> domainBlackCells = new ArrayList<>();
        List<AssetCell> rejectCells = new ArrayList<>();
        List<AssetCell> unreviewedCells = new ArrayList<>();
        List<AssetCell> deletedCells = new ArrayList<>();
        // 索引库判断文档是否存在 是否被黑名单过滤 是否被审核拒绝
        for (AssetBucketDTO bucket : bucketDTOList4Idx) {
            Optional<AssetCell> cellOptional = getAssetCell(goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl(), goodCaseContext.getInput().getRegion(), bucket);
            if (cellOptional.isPresent()) {
                AssetCell assetCell = cellOptional.get();
                foundCells.add(assetCell);
                if (isInBlackList(bucket, assetCell)) {
                    domainBlackCells.add(assetCell);
                }

                if (isAssetCellRejected(assetCell)) {
                    rejectCells.add(assetCell);
                }

                if (isAssetCellUnreviewed(assetCell)) {
                    unreviewedCells.add(assetCell);
                }

                if (isAssetCellDeleted(assetCell)) {
                    deletedCells.add(assetCell);
                }
            }
        }

        existCheckResult.setFoundCellsFromSet(foundCells);

        if (!foundCells.isEmpty()) {
            existCheckResult.setCrawled(true);
            // 如果文档存在，存入上下文中
            goodCaseContext.setDoc(foundCells.getFirst());

            if (foundCells.size() == domainBlackCells.size()) {
                // 全部被黑名单过滤
                existCheckResult.setReason4Disabled();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == rejectCells.size()) {
                // 全部被审核拒绝
                existCheckResult.setReason4Rejected();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == unreviewedCells.size()) {
                // 全部待审核
                existCheckResult.setReason4Unreviewed();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == deletedCells.size()) {
                // 全部待删除
                existCheckResult.setReason4Deleted();
                existCheckResult.setEnd(true);
                return;
            }

            List<AssetCell> statusOkCells = foundCells.stream().filter(cell -> !rejectCells.contains(cell) && !domainBlackCells.contains(cell) && !unreviewedCells.contains(cell) && !deletedCells.contains(cell))
                    .toList();
            if (!statusOkCells.isEmpty()) {
                log.info("doc在索引库中可被检索, id:{}, url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                existCheckResult.setEnd(true);
            } else {
                // 其他状态 因为上面已经判断过全部被拒绝、黑名单、待审核、删除的情况，所以这里剩下的状态只能是部分被拒绝、部分黑名单、部分待审核、部分删除
                existCheckResult.setReason4Disabled();
                log.error("doc在索引库中被多种原因过滤掉, id:{}, url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                existCheckResult.setEnd(true);
            }
        }
    }

    private void existCheck4Set(ExistCheckResult existCheckResult, GoodCaseContext goodCaseContext, List<AssetBucketDTO> bucketDTOList4Set) {
        List<AssetCell> foundCells = new ArrayList<>();
        List<AssetCell> rejectCells = new ArrayList<>();
        List<AssetCell> unreviewedCells = new ArrayList<>();
        List<AssetCell> deletedCells = new ArrayList<>();
        List<AssetCell> deDupedCells = new ArrayList<>();
        // 数据集判断文档是否存在 是否被审核拒绝
        for (AssetBucketDTO bucket : bucketDTOList4Set) {
            Optional<AssetCell> cellOptional = getAssetCell(goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl(), goodCaseContext.getInput().getRegion(), bucket);
            if (cellOptional.isPresent()) {
                AssetCell assetCell = cellOptional.get();
                foundCells.add(assetCell);

                if (isAssetCellRejected(assetCell)) {
                    rejectCells.add(assetCell);
                }

                if (isAssetCellUnreviewed(assetCell)) {
                    unreviewedCells.add(assetCell);
                }

                if (isAssetCellDeleted(assetCell)) {
                    deletedCells.add(assetCell);
                }

                if (isAssetCellDeDuped(assetCell)) {
                    deDupedCells.add(assetCell);
                }
            }
        }

        if (!foundCells.isEmpty()) {
            existCheckResult.setCrawled(true);
            if (goodCaseContext.getDoc() == null) {
                goodCaseContext.setDoc(foundCells.getFirst());
            }

            if (foundCells.size() == rejectCells.size()) {
                // 全部被审核拒绝
                existCheckResult.setReason4Rejected();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == unreviewedCells.size()) {
                // 全部待审核
                existCheckResult.setReason4Unreviewed();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == deletedCells.size()) {
                // 全部待删除
                existCheckResult.setReason4Deleted();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == deDupedCells.size()) {
                // 全部被去重
                existCheckResult.setReason4Dup();
                existCheckResult.setEnd(true);
                return;
            }

            List<AssetCell> statusOkCells = foundCells.stream().filter(cell -> !rejectCells.contains(cell)
                            && !unreviewedCells.contains(cell)
                            && !deletedCells.contains(cell)
                            && !deDupedCells.contains(cell))
                    .toList();
            if (!statusOkCells.isEmpty()) {
                log.info("doc在数据集中存在状态正常记录, id:{}, url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                existCheckResult.setEnd(true);
            } else {
                // 其他状态 因为上面已经判断过全部被拒绝、待审核、删除的情况，所以这里剩下的状态只能是部分被拒绝、部分待审核、部分删除、部分去重
                existCheckResult.setReason4Disabled();
                log.error("doc在数据集中被多种原因过滤掉, id:{}, url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                existCheckResult.setEnd(true);
            }
        }
    }

    private void existCheck4Raw(ExistCheckResult existCheckResult, GoodCaseContext
            goodCaseContext, List<AssetBucketDTO> webAndDocBucketDTOs) {
        List<AssetCell> foundCells = new ArrayList<>();
        List<AssetCell> rejectCells = new ArrayList<>();
        List<AssetCell> unreviewedCells = new ArrayList<>();
        List<AssetCell> deletedCells = new ArrayList<>();

        // 站点判断文档是否存在 是否被审核拒绝
        for (AssetBucketDTO bucket : webAndDocBucketDTOs) {
            Optional<AssetCell> cellOptional = getAssetCell(goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl(), goodCaseContext.getInput().getRegion(), bucket);
            if (cellOptional.isPresent()) {
                AssetCell assetCell = cellOptional.get();
                foundCells.add(assetCell);

                if (isAssetCellRejected(assetCell)) {
                    rejectCells.add(assetCell);
                }

                if (isAssetCellUnreviewed(assetCell)) {
                    unreviewedCells.add(assetCell);
                }

                if (isAssetCellDeleted(assetCell)) {
                    deletedCells.add(assetCell);
                }
            }
        }

        if (!foundCells.isEmpty()) {
            existCheckResult.setCrawled(true);
            if (goodCaseContext.getDoc() == null) {
                goodCaseContext.setDoc(foundCells.getFirst());
            }

            if (foundCells.size() == rejectCells.size()) {
                // 全部被审核拒绝
                existCheckResult.setReason4Rejected();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == unreviewedCells.size()) {
                // 全部待审核
                existCheckResult.setReason4Unreviewed();
                existCheckResult.setEnd(true);
                return;
            }

            if (foundCells.size() == deletedCells.size()) {
                // 全部待删除
                existCheckResult.setReason4Deleted();
                existCheckResult.setEnd(true);
                return;
            }

            List<AssetCell> statusOkCells = foundCells.stream().filter(cell -> !rejectCells.contains(cell)
                            && !unreviewedCells.contains(cell)
                            && !deletedCells.contains(cell))
                    .toList();
            if (!statusOkCells.isEmpty()) {
                log.info("doc在源数据中存在状态正常记录, id:{}, url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                //判断是否被源数据 -> 数据集 中的规则过滤掉
                if (!existCheckResult.getFoundCellsFromSet().isEmpty()) {
                    existCheckResult.setReason4Filtered();
                }
                existCheckResult.setEnd(true);
            } else {
                // 其他状态 因为上面已经判断过全部被拒绝、待审核、删除的情况，所以这里剩下的状态只能是部分被拒绝、部分待审核、部分删除
                existCheckResult.setReason4Disabled();
                log.error("doc在源数据中被多种原因过滤掉, id:{}, url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                existCheckResult.setEnd(true);
            }
        } else {
            // 站点和文档都没有命中，说明未爬取
            existCheckResult.setReason4UnCrawled();
            existCheckResult.setEnd(true);
        }
    }

    /**
     * 判断文档是否在黑名单中
     * 1.文档domain在黑名单中，视为被黑名单过滤
     *
     * @param bucket
     * @param assetCell
     * @return
     */
    private boolean isInBlackList(AssetBucketDTO bucket, AssetCell assetCell) {
        IdxBucketExtConfig extConfig = bucket.getExtConfig().to(IdxBucketExtConfig.class);
        List<String> domainList = domainBlackApi.getDomainList(extConfig.getRegion(), bucket.getCode());
        // 计算被禁用的文档数量
        String domain = assetCell.getString("domain");

        return domain != null && domainList.contains(domain);
    }

    /**
     * 判断文档是否未审核
     *
     * @param assetCell
     * @return
     */
    private boolean isAssetCellUnreviewed(AssetCell assetCell) {
        AssetAuditStatus status = null;
        Optional<AssetCellProps> propsOptional = assetCell.getPropsOptional();
        if (propsOptional.isPresent()) {
            status = propsOptional.get().getAuditStatus();
        }
        return AssetAuditStatus.PRE_INIT.equals(status);
    }

    /**
     * 判断文档是否被拒绝
     *
     * @param assetCell
     * @return
     */
    private boolean isAssetCellRejected(AssetCell assetCell) {
        AssetAuditStatus status = null;
        Optional<AssetCellProps> propsOptional = assetCell.getPropsOptional();
        if (propsOptional.isPresent()) {
            status = propsOptional.get().getAuditStatus();
        }
        return AssetAuditStatus.REJECT.equals(status);
    }

    /**
     * 判断文档是否被删除
     *
     * @param assetCell
     * @return
     */
    private boolean isAssetCellDeleted(AssetCell assetCell) {
        int op = 0;
        Optional<AssetCellProps> propsOptional = assetCell.getPropsOptional();
        if (propsOptional.isPresent()) {
            op = propsOptional.get().getIntValue("op", 0);
        }
        return AssetOperationType.DELETE.getValue() == op;
    }

    /**
     * 判断文档是否被去重
     *
     * @param assetCell
     * @return
     */
    private boolean isAssetCellDeDuped(AssetCell assetCell) {
        Optional<AssetCellProps> propsOptional = assetCell.getPropsOptional();
        if (propsOptional.isPresent()) {
            AssetCellProps assetCellProps = propsOptional.get();
            int s = assetCellProps.getIntValue("s", 0);
            return s == 1;
        }

        return false;
    }

    private Optional<AssetCell> getAssetCell(String id, String url, String region, AssetBucketDTO bucket) {
        // 查询文档
        CellQueryDTO cellQueryDTO = new CellQueryDTO();
        cellQueryDTO.setRegionCode(region);
        cellQueryDTO.setBucketCode(bucket.getCode());
        if (StringUtils.isNotBlank(id)) {
            cellQueryDTO.setIds(Collections.singletonList(id));
        }
        if (StringUtils.isNotBlank(url)) {
            cellQueryDTO.setUrls(Collections.singletonList(url));
        }
        List<AssetCell> docs = this.assetPortalApi.listAssetCell(cellQueryDTO);
        if (CollectionUtils.isEmpty(docs)) {
            return Optional.empty();
        }
        return Optional.of(docs.getFirst());
    }

    private List<AssetBucketDTO> group(List<AssetBucketDTO> bucketList, String kindCode) {
        List<AssetBucketDTO> groupedList = new ArrayList<>();
        for (AssetBucketDTO bucket : bucketList) {
            if (kindCode.equals(bucket.getKindCode())) {
                groupedList.add(bucket);
            }
        }
        return groupedList;
    }
}
