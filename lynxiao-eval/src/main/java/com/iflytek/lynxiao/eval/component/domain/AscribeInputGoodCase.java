package com.iflytek.lynxiao.eval.component.domain;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.pandora.api.Jsonable;

/**
 * 竞品分析组件入参
 *
 * <AUTHOR>  2025/3/6 09:07
 */
@Setter
@Getter
@Accessors(chain = true)
public class AscribeInputGoodCase extends Jsonable {

    /**
     * 请求query
     */
    private String query;

    /**
     * 场景策略流程id
     */
    private String processId;

    /**
     * traceId 历史执行记录的traceId， 用于从es中获取历史执行记录
     */
    private String traceId;

    /**
     * 记录流程执行的traceId
     */
    private String _traceId;

    /**
     * 测评对象id
     */
    private String targetId;

    /**
     * region  hf、sh
     */
    private String region;

    /**
     * 是否需要mock  0：不需要  1：需要
     */
    private int needMock = 1;

    /**
     * 待分析url
     */
    private String goodUrl;

    /**
     * 竞品id
     */
    private String goodId;

    /**
     * payload 流程入参
     */
    private JSONObject payload = new JSONObject();
}
