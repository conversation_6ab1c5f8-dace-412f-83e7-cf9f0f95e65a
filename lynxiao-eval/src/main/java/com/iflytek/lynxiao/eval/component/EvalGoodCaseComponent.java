package com.iflytek.lynxiao.eval.component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.content.ContentApi;
import com.iflytek.lynxiao.eval.component.core.*;
import com.iflytek.lynxiao.eval.component.core.impl.ProcTraceFetcherFactory;
import com.iflytek.lynxiao.eval.component.domain.*;
import com.iflytek.lynxiao.eval.domain.mark.MarkMockData;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetData;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetTrace;
import com.iflytek.lynxiao.eval.entity.MarkMockEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import com.iflytek.lynxiao.eval.repository.MarkMockRepository;
import com.iflytek.lynxiao.eval.repository.MarkTargetRepository;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.FuncServiceHandler;
import skynet.boot.pandora.annotation.SkynetPandoraFuncHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.exception.PandoraException;

import java.util.List;

/**
 * 竞品分析组件
 * <p>
 * 输入：流程id、url，执行完整的goodCase分析过程（包括goodUrl没有召回的预评估过程）
 * </p>
 *
 * <AUTHOR>  2025/3/6 09:03
 */
@Slf4j
@SkynetPandoraFuncHandler
public class EvalGoodCaseComponent implements FuncServiceHandler {

    private final WorkflowProcessService workflowProcessService;
    private final ExistService existService;
    private final ContentApi urlNormalizer;
    private final PreEvalNodeInvoker preEvalNodeInvoker;
    private final ProcTraceFetcherFactory procTraceFetcherFactory;
    private final MarkTargetRepository markTargetRepository;
    private final MarkMockRepository markMockRepository;

    public EvalGoodCaseComponent(WorkflowProcessService workflowProcessService, ExistService existService,
                                 ContentApi urlNormalizer, PreEvalNodeInvoker preEvalNodeInvoker, ProcTraceFetcherFactory procTraceFetcherFactory, MarkTargetRepository markTargetRepository, MarkMockRepository markMockRepository) {
        this.workflowProcessService = workflowProcessService;
        this.existService = existService;
        this.urlNormalizer = urlNormalizer;
        this.preEvalNodeInvoker = preEvalNodeInvoker;
        this.procTraceFetcherFactory = procTraceFetcherFactory;
        this.markTargetRepository = markTargetRepository;
        this.markMockRepository = markMockRepository;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        log.debug("good-case eval start, apiRequest={}", apiRequest);

        AscribeInputGoodCase ascribeInputGoodCase = apiRequest.getPayload().to(AscribeInputGoodCase.class);
        checkInput(ascribeInputGoodCase);
        ascribeInputGoodCase.set_traceId("eval_ascribe_good" + UUID.fastUUID().toString().substring(0, 8));
        log.info("goodcase traceId:{}", ascribeInputGoodCase.get_traceId());

        ProcTraceFetcher procTraceFetcher = procTraceFetcherFactory.buildFetcher(ascribeInputGoodCase.getProcessId(), ascribeInputGoodCase.getTraceId());
        WorkflowProcess workflowProcess = workflowProcessService.getWorkflowProcess(ascribeInputGoodCase.getProcessId(), false);
        if (workflowProcess == null) {
            throw new PandoraException("流程不存在");
        }

        // 0. 构建goodCase分析上下文
        GoodCaseContext goodCaseContext = new GoodCaseContext(ascribeInputGoodCase);

        // 1. 拿到query执行轨迹
        if (StringUtils.isBlank(ascribeInputGoodCase.getTargetId())) {
            fetchTraceLogsByExecProcess(apiRequest, ascribeInputGoodCase, procTraceFetcher, workflowProcess, goodCaseContext);
        } else {
            fetchHistoryTraceLogs(apiRequest, ascribeInputGoodCase, procTraceFetcher, workflowProcess, goodCaseContext);
        }

        // 2. 分析id或者url是否存在
        ExistCheckResult existCheckResult = existService.process(workflowProcess, goodCaseContext);
        goodCaseContext.setExistCheckResult(existCheckResult);

        // 3. 判断是否需要mock，以及能否mock
        if (ascribeInputGoodCase.getNeedMock() == 0) {
            // 不需要mock，直接返回
            log.info("good-case eval not need mock, goodCaseId:{}, goodCaseUrl:{}, doc:{}", ascribeInputGoodCase.getGoodId(), ascribeInputGoodCase.getGoodUrl(), goodCaseContext.getDoc());
            return new ApiResponse(JSONObject.from(goodCaseContext.computeOutput()));
        }
        if (goodCaseContext.getDoc() == null) {
            // id或者url对应的文档doc不存在
            log.warn("good-case eval doc not exist, goodCaseId:{}, goodCaseUrl:{}", ascribeInputGoodCase.getGoodId(), ascribeInputGoodCase.getGoodUrl());
            goodCaseContext.getExistCheckResult().setReason4CrawledFailed();
            return new ApiResponse(JSONObject.from(goodCaseContext.computeOutput()));
        }

        // 4.mock分析
        executeMockAnalysis(goodCaseContext, workflowProcess);

        // 5. 输出结果
        AscribeOutputGoodCase ascribeOutputGoodCase = goodCaseContext.computeOutput();
        return new ApiResponse(JSONObject.from(ascribeOutputGoodCase));
    }

    private void fetchTraceLogsByExecProcess(ApiRequest apiRequest, AscribeInputGoodCase ascribeInputGoodCase, ProcTraceFetcher procTraceFetcher, WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        ProcTraceFetcherParam fetcherParam = ProcTraceFetcherParam.of(ascribeInputGoodCase, apiRequest);
        String processId = procTraceFetcher.fetchWorkflowProcessId(fetcherParam);
        fetcherParam.setProcessId(processId);
        fetcherParam.setWorkflowProcess(workflowProcess);
        ProcTraceFetcherResult fetcherResult = procTraceFetcher.process(fetcherParam);
        List<TraceLogItem> traceLogItems = fetcherResult.getTraceLogItems();
        goodCaseContext.setTraceLogs(traceLogItems);
    }

    private void fetchHistoryTraceLogs(ApiRequest apiRequest, AscribeInputGoodCase ascribeInputGoodCase, ProcTraceFetcher procTraceFetcher, WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        // 获取已有结果
        MarkTargetEntity markTargetEntity = markTargetRepository.findById(ascribeInputGoodCase.getTargetId());
        MarkTargetData data = markTargetEntity.getData();

        //第一次归因时，ascribeTraceList为空 通过执行流程获取到归因全链路
        if (CollectionUtils.isEmpty(data.getAscribeTraceList())) {
            fetchTraceLogsByExecProcess(apiRequest, ascribeInputGoodCase, procTraceFetcher, workflowProcess, goodCaseContext);
            //保存归因链路到数据库
            data.setAscribeTraceList(goodCaseContext.getTraceLogs().stream().map(item -> BeanUtil.copyProperties(item, MarkTargetTrace.class)).toList());
            this.markTargetRepository.save(markTargetEntity);
            return;
        }

        List<TraceLogItem> traceLogItems = data.getAscribeTraceList().stream().map(item -> BeanUtil.copyProperties(item, TraceLogItem.class)).toList();
        goodCaseContext.setTraceLogs(traceLogItems);
    }

    /**
     * mock文档，进行分析
     * 除召回节点外 检查哪些节点需要mock  需要mock的条件是 good doc不存在于该节点的初始返回结果中
     *
     * @param goodCaseContext 分析会话的上下文
     * @param workflowProcess mock需要使用的流程
     */
    private void executeMockAnalysis(GoodCaseContext goodCaseContext, WorkflowProcess workflowProcess) {
        PreEvalNodeChain preEvalNodeChain = new PreEvalNodeChain(goodCaseContext.getTraceLogs());

        preEvalNodeChain.checkNeedMock(goodCaseContext.getDocId(), goodCaseContext.getTraceLogs());

        // 直接执行组件链逻辑
        List<PreEvalNode> allCompChain = preEvalNodeChain.getNodeChain();
        for (PreEvalNode preEvalNode : allCompChain) {
            if (preEvalNode.isCanBeMock()) {
                // 执行组件
                List<JSONObject> outputDocs = preEvalNodeInvoker.process(workflowProcess, goodCaseContext, preEvalNode);
                // 记录执行结果
                goodCaseContext.addCompResult(preEvalNode.getNodeId(), outputDocs);
                // 保存至数据库
                saveMarkMock(goodCaseContext.getInput(), preEvalNode, outputDocs);
            }
        }
    }

    private void saveMarkMock(AscribeInputGoodCase inputGoodCase, PreEvalNode preEvalNode, List<JSONObject> outputDocs) {

        MarkMockEntity markMockEntity = this.markMockRepository.findMockEntity(inputGoodCase.getTargetId(), preEvalNode.getNodeId(), inputGoodCase.getGoodId(), inputGoodCase.getGoodUrl());
        if (markMockEntity == null) {
            markMockEntity = new MarkMockEntity();
            markMockEntity.setTargetId(inputGoodCase.getTargetId());
            markMockEntity.setNodeId(preEvalNode.getNodeId());
        }
        markMockEntity.setDocId(inputGoodCase.getGoodId());
        markMockEntity.setUrl(inputGoodCase.getGoodUrl());
        markMockEntity.setCode(preEvalNode.getCode());
        markMockEntity.setData(MarkMockData.of(outputDocs));
        this.markMockRepository.save(markMockEntity);
    }

    private void checkInput(AscribeInputGoodCase ascribeInputGoodCase) {

        Assert.notNull(ascribeInputGoodCase, "请求参数不能为空");
        // processId和traceId不能同时为空
        if (StringUtils.isAllBlank(ascribeInputGoodCase.getProcessId(), ascribeInputGoodCase.getTraceId())) {
            throw new LynxiaoException("processId和traceId不能同时为空");
        }

        Assert.hasText(ascribeInputGoodCase.getProcessId(), "processId can not be empty");
        Assert.hasText(ascribeInputGoodCase.getQuery(), "query can not be empty");
        Assert.isTrue(StringUtils.isNotBlank(ascribeInputGoodCase.getGoodId())
                || StringUtils.isNotBlank(ascribeInputGoodCase.getGoodUrl()), "goodId and goodUrl can not be empty at the same time");

        if (StringUtils.isNotBlank(ascribeInputGoodCase.getGoodUrl())) {
            // url规范化
            String normalizedUrl = this.urlNormalizer.normalizeUrl(ascribeInputGoodCase.getGoodUrl());
            if (StringUtils.isBlank(normalizedUrl)) {
                log.error("normalizeUrl failed, url:{}", ascribeInputGoodCase.getGoodUrl());
                throw new LynxiaoException("规范后的url为空，请检查输入的url:" + ascribeInputGoodCase.getGoodUrl());
            }
            ascribeInputGoodCase.setGoodUrl(normalizedUrl);
        }
    }
}
