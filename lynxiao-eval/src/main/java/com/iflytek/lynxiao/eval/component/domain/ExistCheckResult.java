package com.iflytek.lynxiao.eval.component.domain;

import com.iflytek.lynxiao.data.domain.AssetCell;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ExistCheckResult {

    /**
     * 存在性判断是否结束
     */
    private boolean end;

    /**
     * 数据集合中找到的记录
     */
    private List<AssetCell> foundCellsFromSet = new ArrayList<>();

    private List<EvalResultItem> result = new ArrayList<>();

    // 数据是否被爬取
    private boolean crawled = true;

    public ExistCheckResult() {
        result.add(new EvalResultItem("被去重"));
        result.add(new EvalResultItem("被站点规则筛除"));
        result.add(new EvalResultItem("未爬取"));
        result.add(new EvalResultItem("爬取失败"));
        result.add(new EvalResultItem("被黑名单过滤"));
        result.add(new EvalResultItem("审核未通过"));
        result.add(new EvalResultItem("待审核"));
        result.add(new EvalResultItem("待删除"));
    }


    public void setReason4Disabled() {
        setReason("被黑名单过滤", 1);
    }

    public void setReason4Rejected() {
        setReason("审核未通过", 1);
    }

    public void setReason4Unreviewed() {
        setReason("待审核", 1);
    }

    public void setReason4Deleted() {
        setReason("待删除", 1);
    }

    public void setReason4Dup() {
        setReason("被去重", 1);
    }

    public void setReason4Filtered() {
        setReason("被站点规则筛除", 1);
    }

    public void setReason4UnCrawled() {
        this.crawled = false;
        setReason("未爬取", 1);
    }

    public void setReason4CrawledFailed() {
        this.crawled = true;
        setReason("爬取失败", 1);
    }

    private void setReason(String name, int value) {
        if (result != null) {
            result.forEach(item -> {
                if (item.getName().equals(name)) {
                    item.setValue(value);
                }
            });
        }
    }
}